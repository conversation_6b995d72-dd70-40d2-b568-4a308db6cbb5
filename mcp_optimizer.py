🧑‍💻 You:
Give me detailed costing of bedrock of last 10 days
🤖 Enhanced Assistant:
The request for detailed costing of bedrock for the last 10 days is not related to any of the available tools. The tools provided are for managing and interacting with AWS resources, such as listing, creating, updating, or deleting them, but they do not provide financial or cost management information. Therefore, I cannot directly use any of the tools to fulfill this request. I need to inform the user that I cannot help with this request using the available tools. I'm sorry, but I cannot provide detailed costing information for bedrock for the last 10 days using the available tools. The tools provided are for managing and interacting with AWS resources, such as listing, creating, updating, or deleting them, but they do not provide financial or cost management information. If you need help with AWS resource management, I can assist with that. For cost management, you may need to use AWS Cost Explorer or other financial management tools within the AWS ecosystem.
🧑‍💻 You:
Give me detailed analysis cost of my account in ap-south-1
🤖 Enhanced Assistant:
Based on the suggestions provided, here are some AWS CLI commands that could help you analyze the cost of your account in the `ap-south-1` region:
Retrieve Savings Plans Coverage:

aws ce get-savings-plans-coverage --region ap-south-1 --time-period Start=2023-01-01,End=2023-12-31 --granularity DAILY

This command will help you understand how much of your cost is covered by Savings Plans over a specified period.

Estimate Cost for Commitment Purchase:

aws ce start-commitment-purchase-analysis --region ap-south-1 --commitment-purchase-analysis-configuration '{"CommitmentPurchaseAnalysisConfiguration": {"AccountId": "your-account-id", "AnalysisType": "SPOT_INSTANCES_TO_ADD"}}'

This command will estimate the cost, coverage, and utilization impact of your planned commitment purchases.

Get Bill Estimate:

aws bcm-pricing-calculator get-bill-estimate --identifier your-estimate-id --region ap-south-1

This command will retrieve details of a specific bill estimate.

Get Free Tier Account Plan State:

aws freetier get-account-plan-state --region ap-south-1

This command will return information related to the state of the account plan related to Free Tier.

Get Account Information:

aws apigateway get-account --region ap-south-1

This command will provide information about the current Account resource.

You can use these commands to get a detailed cost analysis of your AWS account in the ap-south-1 region. If you need further assistance with executing these commands or interpreting the results, feel free to ask."""
MCP Server Optimization Framework
Provides intelligent parameter validation, error recovery, and performance optimization
for MCP-based systems with AWS service integration.
"""

import json
import logging
import re
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from difflib import get_close_matches

logger = logging.getLogger(__name__)


class CostExplorerOptimizer:
    """Optimizer for AWS Cost Explorer tools with intelligent parameter correction"""

    def __init__(self):
        self.date_constraints = {
            'max_lookback_months': 14,
            'historical_requires_first_of_month': True
        }
        self.valid_dimensions = [
            'SERVICE', 'REGION', 'AZ', 'INSTANCE_TYPE', 'USAGE_TYPE',
            'OPERATING_SYSTEM', 'TENANCY', 'LINKED_ACCOUNT', 'BILLING_ENTITY'
        ]

    def validate_and_correct_date_range(self, date_range: Dict[str, str]) -> Dict[str, str]:
        """Auto-correct date ranges for AWS Cost Explorer constraints"""
        try:
            start_date = datetime.fromisoformat(date_range['start_date'])
            end_date = datetime.fromisoformat(date_range['end_date'])
            now = datetime.now()

            # Check if date range exceeds 14 months
            fourteen_months_ago = now - timedelta(days=14*30)

            if start_date < fourteen_months_ago:
                # For historical data, end date must be first day of month
                if end_date.day != 1:
                    corrected_end = end_date.replace(day=1)
                    logger.info(f"Auto-corrected end date from {end_date.date()} to {corrected_end.date()}")
                    date_range['end_date'] = corrected_end.isoformat()

            # Ensure end date is not in the future
            if end_date > now:
                logger.warning(f"End date {end_date.date()} is in the future, adjusting to today")
                date_range['end_date'] = now.replace(hour=23, minute=59, second=59).isoformat()

            return date_range

        except (KeyError, ValueError) as e:
            logger.error(f"Invalid date format in date_range: {e}")
            # Return a safe default date range
            now = datetime.now()
            return {
                'start_date': (now - timedelta(days=30)).isoformat(),
                'end_date': now.isoformat()
            }

    def validate_filter_expression(self, filter_expression: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and optimize filter expressions"""
        if not filter_expression:
            return filter_expression

        # Ensure proper structure for complex filters
        if 'And' in filter_expression or 'Or' in filter_expression:
            operator = 'And' if 'And' in filter_expression else 'Or'
            filters = filter_expression[operator]

            # Validate each filter in the list
            validated_filters = []
            for f in filters:
                validated_filter = self._validate_single_filter(f)
                if validated_filter:
                    validated_filters.append(validated_filter)

            if validated_filters:
                filter_expression[operator] = validated_filters
            else:
                # If no valid filters, remove the complex filter
                del filter_expression[operator]

        elif any(key in filter_expression for key in ['Dimensions', 'Tags', 'CostCategories']):
            # Single filter validation
            validated = self._validate_single_filter(filter_expression)
            if validated:
                filter_expression = validated
            else:
                return {}  # Return empty if invalid

        return filter_expression

    def _validate_single_filter(self, filter_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Validate a single filter expression"""
        try:
            if 'Dimensions' in filter_dict:
                dim_filter = filter_dict['Dimensions']
                dimension_key = dim_filter.get('Key', '').upper()

                # Validate dimension key
                if dimension_key not in self.valid_dimensions:
                    logger.warning(f"Invalid dimension key: {dimension_key}")
                    # Try to find closest match
                    closest = get_close_matches(dimension_key, self.valid_dimensions, n=1)
                    if closest:
                        logger.info(f"Suggesting dimension key: {closest[0]} instead of {dimension_key}")
                        dim_filter['Key'] = closest[0]
                    else:
                        return None

                # Validate values exist
                if 'Values' not in dim_filter or not dim_filter['Values']:
                    logger.warning("Empty dimension values in filter")
                    return None

                # Validate MatchOptions
                if 'MatchOptions' in dim_filter:
                    valid_options = ['EQUALS', 'CASE_SENSITIVE']
                    match_options = dim_filter['MatchOptions']
                    if isinstance(match_options, list):
                        dim_filter['MatchOptions'] = [opt for opt in match_options if opt in valid_options]
                        if not dim_filter['MatchOptions']:
                            dim_filter['MatchOptions'] = ['EQUALS']

            return filter_dict

        except Exception as e:
            logger.error(f"Error validating filter: {e}")
            return None

    def pre_validate_tool_call(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Pre-validate parameters before tool execution"""
        if tool_name in ['get_cost_and_usage', 'get_dimension_values', 'get_cost_forecast']:
            # Validate date range
            if 'date_range' in parameters:
                parameters['date_range'] = self.validate_and_correct_date_range(
                    parameters['date_range']
                )

            # Validate filter expression
            if 'filter_expression' in parameters:
                parameters['filter_expression'] = self.validate_filter_expression(
                    parameters['filter_expression']
                )

        elif tool_name == 'get_cost_and_usage_comparisons':
            # Validate both date ranges
            if 'baseline_date_range' in parameters:
                parameters['baseline_date_range'] = self.validate_and_correct_date_range(
                    parameters['baseline_date_range']
                )
            if 'comparison_date_range' in parameters:
                parameters['comparison_date_range'] = self.validate_and_correct_date_range(
                    parameters['comparison_date_range']
                )

        return parameters


class CloudFormationOptimizer:
    """Optimizer for AWS CloudFormation tools"""

    def __init__(self):
        self.valid_resource_types = [
            'AWS::EC2::Instance', 'AWS::S3::Bucket', 'AWS::RDS::DBInstance',
            'AWS::Lambda::Function', 'AWS::IAM::Role', 'AWS::CloudFormation::Stack',
            'AWS::EC2::SecurityGroup', 'AWS::EC2::VPC', 'AWS::EC2::Subnet'
        ]
        self.region_mapping = {
            'us-east-1': 'N. Virginia',
            'ap-south-1': 'Mumbai',
            'eu-west-1': 'Ireland',
            'ap-southeast-1': 'Singapore',
            'us-west-2': 'Oregon'
        }

    def validate_resource_type(self, resource_type: str) -> str:
        """Validate CloudFormation resource types with suggestions"""
        if resource_type in self.valid_resource_types:
            return resource_type

        # Find closest match
        closest = get_close_matches(resource_type, self.valid_resource_types, n=1)
        if closest:
            logger.info(f"Suggesting resource type: {closest[0]} instead of {resource_type}")
            return closest[0]

        logger.warning(f"Unknown resource type: {resource_type}")
        return resource_type

    def validate_region(self, region: str) -> str:
        """Validate AWS region format"""
        import re
        if re.match(r'^[a-z]{2}-[a-z]+-\d+$', region):
            return region

        logger.warning(f"Invalid region format: {region}")
        return 'us-east-1'  # Default fallback

    def optimize_patch_document(self, patch_document: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Optimize RFC 6902 patch documents"""
        optimized = []

        for operation in patch_document:
            # Validate operation structure
            if not all(key in operation for key in ['op', 'path']):
                logger.warning(f"Invalid patch operation: {operation}")
                continue

            # Validate operation type
            if operation['op'] not in ['add', 'remove', 'replace', 'move', 'copy', 'test']:
                logger.warning(f"Invalid patch operation type: {operation['op']}")
                continue

            # Optimize path expressions (remove unnecessary escaping)
            if 'path' in operation:
                operation['path'] = operation['path'].replace('\\/', '/')

            optimized.append(operation)

        return optimized

    def pre_validate_tool_call(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Pre-validate parameters before tool execution"""
        if tool_name in ['list_resources', 'get_resource', 'update_resource', 'delete_resource']:
            # Validate resource type
            if 'resource_type' in parameters:
                parameters['resource_type'] = self.validate_resource_type(
                    parameters['resource_type']
                )

            # Validate region
            if 'region' in parameters:
                parameters['region'] = self.validate_region(parameters['region'])

        elif tool_name == 'update_resource':
            # Optimize patch document
            if 'patch_document' in parameters:
                parameters['patch_document'] = self.optimize_patch_document(
                    parameters['patch_document']
                )

        return parameters


class AWSAPIOptimizer:
    """Optimizer for AWS API tools"""

    def __init__(self):
        self.cli_patterns = {
            'list_instances': r'aws ec2 describe-instances',
            'list_buckets': r'aws s3 ls',
            'get_instance': r'aws ec2 describe-instances --instance-ids',
            'create_bucket': r'aws s3 mb'
        }
        self.parameter_validators = {
            '--region': self.validate_region,
            '--instance-id': self.validate_instance_id,
            '--bucket': self.validate_bucket_name
        }

    def validate_cli_command(self, command: str) -> bool:
        """Validate AWS CLI command structure"""
        if not command.startswith('aws '):
            raise ValueError("Command must start with 'aws'")

        # Check for dangerous operations
        dangerous_patterns = ['delete', 'terminate', 'stop']
        for pattern in dangerous_patterns:
            if pattern in command.lower():
                logger.warning(f"Potentially dangerous command detected: {command}")

        return True

    def optimize_cli_parameters(self, command: str) -> str:
        """Optimize CLI command parameters"""
        # Add default region if missing
        if '--region' not in command:
            command += ' --region ap-south-1'

        # Add output format for consistency
        if '--output' not in command:
            command += ' --output json'

        return command

    def validate_region(self, region: str) -> str:
        """Validate AWS region format"""
        import re
        if not re.match(r'^[a-z]{2}-[a-z]+-\d+$', region):
            raise ValueError(f"Invalid region format: {region}")
        return region

    def validate_instance_id(self, instance_id: str) -> str:
        """Validate EC2 instance ID format"""
        import re
        if not re.match(r'^i-[a-f0-9]{8,17}$', instance_id):
            raise ValueError(f"Invalid instance ID format: {instance_id}")
        return instance_id

    def validate_bucket_name(self, bucket_name: str) -> str:
        """Validate S3 bucket name format"""
        if not 3 <= len(bucket_name) <= 63:
            raise ValueError(f"Bucket name must be between 3 and 63 characters: {bucket_name}")

        import re
        if not re.match(r'^[a-z0-9][a-z0-9-]*[a-z0-9]$', bucket_name):
            raise ValueError(f"Invalid bucket name format: {bucket_name}")

        return bucket_name

    def pre_validate_tool_call(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Pre-validate parameters before tool execution"""
        if tool_name == 'call_aws':
            if 'cli_command' in parameters:
                # Validate command
                self.validate_cli_command(parameters['cli_command'])

                # Optimize parameters
                parameters['cli_command'] = self.optimize_cli_parameters(
                    parameters['cli_command']
                )

        elif tool_name == 'suggest_aws_commands':
            # Validate query parameter
            if 'query' in parameters:
                query = parameters['query']
                if not query or len(query.strip()) < 3:
                    raise ValueError("Query must be at least 3 characters long")

        return parameters


class AWSPricingOptimizer:
    """Optimizer for AWS Pricing tools"""

    def __init__(self):
        self.connection_retry_config = {
            'max_retries': 3,
            'retry_delay': 5,
            'timeout_increase': 10
        }

    def handle_connection_timeout(self, server_config) -> bool:
        """Handle AWS Pricing server connection timeouts"""
        for attempt in range(self.connection_retry_config['max_retries']):
            try:
                # Increase timeout for each retry
                timeout = server_config.get('timeout', 30) + (
                    attempt * self.connection_retry_config['timeout_increase']
                )

                # Attempt connection with increased timeout
                success = self.connect_with_timeout(server_config, timeout)
                if success:
                    return True

            except Exception as e:
                logger.warning(f"Connection attempt {attempt + 1} failed: {e}")
                if attempt < self.connection_retry_config['max_retries'] - 1:
                    time.sleep(self.connection_retry_config['retry_delay'])

        return False

    def connect_with_timeout(self, server_config, timeout: int) -> bool:
        """Attempt connection with specified timeout"""
        # This would integrate with the actual connection logic
        # For now, return True as the server is already connected
        return True

    def pre_validate_tool_call(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Pre-validate parameters before tool execution"""
        # AWS Pricing tools generally don't need complex parameter validation
        # Most validation is handled by the pricing service itself
        return parameters


class IntelligentErrorRecovery:
    """Intelligent error recovery system for MCP tools"""

    def __init__(self):
        self.error_patterns = {
            'cost_explorer_date_range': {
                'pattern': r'beyond the past \d+ months',
                'recovery_action': 'adjust_date_range'
            },
            'cost_explorer_invalid_dimension': {
                'pattern': r'Invalid value .* for dimension',
                'recovery_action': 'fetch_valid_dimensions'
            },
            'cost_explorer_end_date_format': {
                'pattern': r'end date needs to be the first day of the month',
                'recovery_action': 'adjust_end_date'
            },
            'cloudformation_invalid_resource': {
                'pattern': r'Invalid resource type',
                'recovery_action': 'suggest_similar_resources'
            },
            'aws_api_invalid_command': {
                'pattern': r'Invalid choice|Unknown options',
                'recovery_action': 'validate_command_syntax'
            }
        }

    def analyze_error(self, error_message: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze error and suggest recovery actions"""
        for error_type, config in self.error_patterns.items():
            if re.search(config['pattern'], error_message, re.IGNORECASE):
                return {
                    'error_type': error_type,
                    'recovery_action': config['recovery_action'],
                    'context': context
                }

        return None

    def apply_recovery_strategy(self, error_type: str, error_message: str,
                              context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply specific recovery strategies"""
        strategies = {
            'adjust_date_range': self.adjust_date_range_strategy,
            'fetch_valid_dimensions': self.fetch_valid_dimensions_strategy,
            'adjust_end_date': self.adjust_end_date_strategy,
            'suggest_similar_resources': self.suggest_resources_strategy,
            'validate_command_syntax': self.validate_command_strategy
        }

        if error_type in strategies:
            return strategies[error_type](error_message, context)

        return context.get('original_params', {})

    def adjust_date_range_strategy(self, error_message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Adjust date range to valid constraints"""
        params = context.get('original_params', {}).copy()

        if 'date_range' in params:
            optimizer = CostExplorerOptimizer()
            params['date_range'] = optimizer.validate_and_correct_date_range(
                params['date_range']
            )

        logger.info("Applied date range adjustment recovery strategy")
        return params

    def fetch_valid_dimensions_strategy(self, error_message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch valid dimensions and update parameters"""
        params = context.get('original_params', {}).copy()

        # Extract dimension type from error message
        dimension_match = re.search(r'dimension (\w+)', error_message, re.IGNORECASE)
        if dimension_match:
            dimension_type = dimension_match.group(1).upper()

            # For REGION dimension, we know valid values
            if dimension_type == 'REGION':
                # Update with known valid region
                if 'filter_expression' in params:
                    filter_expr = params['filter_expression']
                    if 'And' in filter_expr:
                        for f in filter_expr['And']:
                            if 'Dimensions' in f and f['Dimensions'].get('Key') == 'REGION':
                                f['Dimensions']['Values'] = ['ap-south-1']
                                logger.info("Updated REGION filter with valid value: ap-south-1")

        return params

    def adjust_end_date_strategy(self, error_message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Adjust end date to first day of month"""
        params = context.get('original_params', {}).copy()

        if 'date_range' in params:
            end_date = datetime.fromisoformat(params['date_range']['end_date'])
            corrected_end = end_date.replace(day=1)
            params['date_range']['end_date'] = corrected_end.isoformat()

            logger.info(f"Adjusted end date to first of month: {corrected_end.date()}")

        return params

    def suggest_resources_strategy(self, error_message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest similar resource types"""
        params = context.get('original_params', {}).copy()

        if 'resource_type' in params:
            optimizer = CloudFormationOptimizer()
            suggested_type = optimizer.validate_resource_type(params['resource_type'])
            if suggested_type != params['resource_type']:
                params['resource_type'] = suggested_type
                logger.info(f"Suggested resource type: {suggested_type}")

        return params

    def validate_command_strategy(self, error_message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and fix AWS CLI command syntax"""
        params = context.get('original_params', {}).copy()

        if 'cli_command' in params:
            optimizer = AWSAPIOptimizer()
            try:
                params['cli_command'] = optimizer.optimize_cli_parameters(
                    params['cli_command']
                )
                logger.info("Optimized AWS CLI command syntax")
            except Exception as e:
                logger.error(f"Failed to optimize command: {e}")

        return params


class UnifiedParameterValidator:
    """Unified parameter validation framework across all servers"""

    def __init__(self):
        self.server_validators = {
            'cost-explorer': CostExplorerOptimizer(),
            'cloudformation': CloudFormationOptimizer(),
            'aws-api': AWSAPIOptimizer(),
            'aws-pricing': AWSPricingOptimizer()
        }
        self.error_recovery = IntelligentErrorRecovery()

    def validate_parameters(self, server_name: str, tool_name: str,
                          parameters: Dict[str, Any]) -> Tuple[Dict[str, Any], List[str]]:
        """Unified parameter validation across all servers"""
        errors = []

        try:
            if server_name in self.server_validators:
                validator = self.server_validators[server_name]
                parameters = validator.pre_validate_tool_call(tool_name, parameters)
            else:
                logger.warning(f"No validator found for server: {server_name}")

        except Exception as e:
            errors.append(f"Parameter validation error: {str(e)}")
            logger.error(f"Parameter validation failed for {server_name}:{tool_name}: {e}")

        return parameters, errors

    def handle_tool_error(self, server_name: str, tool_name: str, error_message: str,
                         original_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle tool execution errors with recovery strategies"""
        context = {
            'server_name': server_name,
            'tool_name': tool_name,
            'original_params': original_params
        }

        # Analyze the error
        error_analysis = self.error_recovery.analyze_error(error_message, context)

        if error_analysis:
            logger.info(f"Applying recovery strategy: {error_analysis['recovery_action']}")

            # Apply recovery strategy
            recovered_params = self.error_recovery.apply_recovery_strategy(
                error_analysis['error_type'],
                error_message,
                context
            )

            return recovered_params

        return None

    def get_server_constraints(self, server_name: str) -> Dict[str, Any]:
        """Get server-specific constraints and limitations"""
        if server_name in self.server_validators:
            validator = self.server_validators[server_name]
            if hasattr(validator, 'get_constraints'):
                return validator.get_constraints()

        # Default constraints
        return {
            'max_execution_time': 30,
            'retry_on_failure': True,
            'validate_parameters': True
        }


# Global instances for use across the application
parameter_validator = UnifiedParameterValidator()
cost_explorer_optimizer = CostExplorerOptimizer()
cloudformation_optimizer = CloudFormationOptimizer()
aws_api_optimizer = AWSAPIOptimizer()
aws_pricing_optimizer = AWSPricingOptimizer()
error_recovery = IntelligentErrorRecovery()