#!/usr/bin/env python3
"""
Direct Cost Analysis Test Script
Tests the MCP cost analysis tools directly
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Import our MCP components
from main import MCPClientManager, DEFAULT_MCP_SERVERS

load_dotenv()

async def test_cost_analysis():
    """Test cost analysis functionality directly"""
    
    print("🔍 Testing AWS Cost Analysis Tools")
    print("=" * 50)
    
    # Initialize MCP client manager
    manager = MCPClientManager()
    
    try:
        # Configure servers
        print("📡 Configuring MCP servers...")
        for server_config in DEFAULT_MCP_SERVERS:
            if server_config.enabled:
                print(f"Adding server: {server_config.name}")
                success = await manager.add_server(server_config)
                if success:
                    print(f"✅ {server_config.name} connected successfully")
                else:
                    print(f"❌ Failed to connect to {server_config.name}")

        # Get available tools
        tools = manager.get_available_tools()
        print(f"✅ Found {len(tools)} available tools")
        
        # List cost-related tools
        cost_tools = [name for name in tools.keys() if 'cost' in name.lower() or 'billing' in name.lower()]
        print(f"💰 Cost-related tools: {cost_tools}")
        
        # Test 1: Get cost and usage for last 10 days
        print("\n📊 Test 1: Bedrock costs for last 10 days")
        print("-" * 40)
        
        # Calculate date range (last 10 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=10)
        
        cost_params = {
            "date_range": {
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d")
            },
            "granularity": "DAILY",
            "group_by": [{"type": "DIMENSION", "key": "SERVICE"}],
            "filter_expression": {
                "dimensions": {
                    "key": "SERVICE",
                    "values": ["Amazon Bedrock"],
                    "match_options": ["EQUALS"]
                }
            }
        }
        
        if "cost-explorer::get_cost_and_usage" in tools:
            print(f"📈 Getting Bedrock costs from {start_date} to {end_date}")
            result = await manager.call_tool("cost-explorer", "get_cost_and_usage", cost_params)
            
            if result["success"]:
                print("✅ Bedrock cost analysis successful!")
                cost_data = result["result"]
                print(json.dumps(cost_data, indent=2))
            else:
                print(f"❌ Bedrock cost analysis failed: {result.get('error', 'Unknown error')}")
        
        # Test 2: Get overall account costs for ap-south-1
        print("\n🌏 Test 2: Account costs in ap-south-1 region")
        print("-" * 40)
        
        region_params = {
            "date_range": {
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d")
            },
            "granularity": "DAILY",
            "group_by": [{"type": "DIMENSION", "key": "SERVICE"}],
            "filter_expression": {
                "dimensions": {
                    "key": "REGION",
                    "values": ["ap-south-1"],
                    "match_options": ["EQUALS"]
                }
            }
        }
        
        if "cost-explorer::get_cost_and_usage" in tools:
            print(f"📊 Getting ap-south-1 costs from {start_date} to {end_date}")
            result = await manager.call_tool("cost-explorer", "get_cost_and_usage", region_params)
            
            if result["success"]:
                print("✅ Regional cost analysis successful!")
                cost_data = result["result"]
                print(json.dumps(cost_data, indent=2))
            else:
                print(f"❌ Regional cost analysis failed: {result.get('error', 'Unknown error')}")
        
        # Test 3: Get cost forecast
        print("\n🔮 Test 3: Cost forecast")
        print("-" * 40)
        
        forecast_params = {
            "date_range": {
                "start": end_date.strftime("%Y-%m-%d"),
                "end": (end_date + timedelta(days=30)).strftime("%Y-%m-%d")
            },
            "granularity": "MONTHLY",
            "metric": "BLENDED_COST"
        }
        
        if "cost-explorer::get_cost_forecast" in tools:
            print("🔮 Getting 30-day cost forecast")
            result = await manager.call_tool("cost-explorer", "get_cost_forecast", forecast_params)
            
            if result["success"]:
                print("✅ Cost forecast successful!")
                forecast_data = result["result"]
                print(json.dumps(forecast_data, indent=2))
            else:
                print(f"❌ Cost forecast failed: {result.get('error', 'Unknown error')}")
        
    except Exception as e:
        print(f"❌ Error during cost analysis: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        await manager.cleanup()
        print("✅ Cleanup complete")

if __name__ == "__main__":
    asyncio.run(test_cost_analysis())
